[2025-06-30 14:02:59] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:02:59] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:02:59] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:02:59] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:02:59] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:02:59] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:02:59] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:02:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:03:26] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:03:27] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:03:27] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:03:27] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:03:27] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:03:27] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:03:27] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:03:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:04:14] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:04:14] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:14] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:14] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:14] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:14] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:14] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:04:16] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:04:16] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:16] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:16] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:16] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:16] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:04:16] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:04:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:05:27] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:05:28] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:05:28] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:05:28] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:05:28] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:05:28] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:05:28] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:05:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:08:39] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:08:39] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:08:39] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:08:39] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:08:39] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:08:39] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:08:39] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:08:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:09:51] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:09:51] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:51] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:51] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:51] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:51] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:51] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:09:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:09:54] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:09:55] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:55] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:55] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:55] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:55] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:09:55] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:09:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:10:06] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:10:06] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:06] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:06] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:06] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:06] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:06] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:10:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:10:08] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:10:08] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:08] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:08] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:08] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:08] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:08] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:10:09] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:10:09] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:09] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:09] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:09] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:09] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:09] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:10:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:10:18] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:10:19] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:19] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:19] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:19] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:19] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:19] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:10:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:10:21] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:10:21] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:21] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:21] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:21] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:21] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:21] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:10:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:10:27] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:10:27] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:27] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:27] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:27] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:27] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:10:27] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:10:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:12:29] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:12:29] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:29] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:29] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:29] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:29] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:29] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:12:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:12:30] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:12:30] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:30] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:30] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:30] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:30] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:30] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:12:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:12:35] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:12:35] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:35] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:35] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:35] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:35] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:35] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:12:36] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:12:37] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:37] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:37] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:37] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:37] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:37] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:12:39] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:12:39] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:39] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:39] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:39] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:39] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:39] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:12:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:12:43] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:12:43] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:43] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:43] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:43] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:43] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:43] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:12:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:12:46] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:12:46] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:46] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:46] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:46] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:46] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:46] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:12:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:12:48] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:12:48] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:48] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:48] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:48] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:48] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:12:48] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:12:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:13:48] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:13:48] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:13:48] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:13:48] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:13:48] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:13:48] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:13:48] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:13:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:14:36] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:14:37] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:14:37] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:14:37] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:14:37] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:14:37] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:14:37] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:14:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:15:04] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:15:04] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:04] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:04] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:04] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:04] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:04] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:15:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:15:23] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:15:23] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:23] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:23] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:23] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:23] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:15:23] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:15:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:17:28] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:17:28] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:28] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:28] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:28] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:28] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:28] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:17:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:17:56] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:17:56] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:56] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:56] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:56] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:56] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:56] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:17:57] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:17:57] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:57] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:57] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:57] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:57] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:17:57] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:17:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:18:05] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:18:06] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:18:06] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:18:06] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:18:06] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:18:06] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:18:06] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:18:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:21:07] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:21:08] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:08] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:08] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:08] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:08] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:08] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:21:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:21:50] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:21:50] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:50] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:50] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:50] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:50] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:21:50] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:21:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:22:18] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:22:18] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:18] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:18] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:18] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:18] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:18] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:22:19] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:22:20] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:20] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:20] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:20] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:20] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:20] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:22:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:22:42] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:22:43] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:43] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:43] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:43] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:43] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:22:43] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:22:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:23:47] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:23:48] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:23:48] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:23:48] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:23:48] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:23:48] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:23:48] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:23:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:24:26] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:24:26] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:26] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:26] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:26] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:26] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:26] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:24:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:24:33] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:24:34] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:34] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:34] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:34] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:34] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:34] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:24:34] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:24:37] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:24:38] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:38] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:38] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:38] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:38] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:38] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:24:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:24:43] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:24:44] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:44] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:44] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:44] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:44] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:24:44] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:24:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:25:17] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-06-30","month":null,"division":null,"site":null} 
[2025-06-30 14:25:17] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:25:17] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:25:17] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:25:17] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:25:17] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-06-30 23:59:59 and `status` = belum po))  
[2025-06-30 14:25:17] local.INFO: Sites Data Response {"count":5} 
[2025-06-30 14:25:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-06-01 00:00:00","used_end_date":"2025-06-30 23:59:59"} 
[2025-06-30 14:26:03] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-06-30 14:26:55] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:26:58] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:27:03] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:27:07] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:47:24] local.INFO: Getting invoice details for ID: 177  
[2025-06-30 14:47:24] local.INFO: Invoice found: 177  
[2025-06-30 14:47:24] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:47:24] local.INFO: Returning invoice details successfully  
[2025-06-30 14:47:24] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:47:44] local.INFO: Getting invoice details for ID: 177  
[2025-06-30 14:47:44] local.INFO: Invoice found: 177  
[2025-06-30 14:47:45] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:47:45] local.INFO: Returning invoice details successfully  
[2025-06-30 14:47:45] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:48:01] local.INFO: Getting invoice details for ID: 177  
[2025-06-30 14:48:01] local.INFO: Invoice found: 177  
[2025-06-30 14:48:01] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:48:01] local.INFO: Returning invoice details successfully  
[2025-06-30 14:48:01] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:48:39] local.INFO: Getting invoice details for ID: 177  
[2025-06-30 14:48:39] local.INFO: Invoice found: 177  
[2025-06-30 14:48:39] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:48:39] local.INFO: Returning invoice details successfully  
[2025-06-30 14:48:39] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:48:56] local.INFO: Getting invoice details for ID: 177  
[2025-06-30 14:48:56] local.INFO: Invoice found: 177  
[2025-06-30 14:48:56] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:48:56] local.INFO: Returning invoice details successfully  
[2025-06-30 14:48:56] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:50:49] local.INFO: Getting invoice details for ID: 179  
[2025-06-30 14:50:49] local.INFO: Invoice found: 179  
[2025-06-30 14:50:49] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:50:49] local.INFO: Returning invoice details successfully  
[2025-06-30 14:50:49] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:51:10] local.INFO: Getting invoice details for ID: 179  
[2025-06-30 14:51:10] local.INFO: Invoice found: 179  
[2025-06-30 14:51:10] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:51:10] local.INFO: Returning invoice details successfully  
[2025-06-30 14:51:10] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:51:40] local.INFO: Getting invoice details for ID: 179  
[2025-06-30 14:51:40] local.INFO: Invoice found: 179  
[2025-06-30 14:51:40] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:51:40] local.INFO: Returning invoice details successfully  
[2025-06-30 14:51:40] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:51:45] local.INFO: Getting invoice details for ID: 179  
[2025-06-30 14:51:45] local.INFO: Invoice found: 179  
[2025-06-30 14:51:45] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:51:45] local.INFO: Returning invoice details successfully  
[2025-06-30 14:51:45] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:52:03] local.INFO: Getting invoice details for ID: 179  
[2025-06-30 14:52:03] local.INFO: Invoice found: 179  
[2025-06-30 14:52:03] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:52:03] local.INFO: Returning invoice details successfully  
[2025-06-30 14:52:03] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:53:28] local.INFO: Getting invoice details for ID: 179  
[2025-06-30 14:53:28] local.INFO: Invoice found: 179  
[2025-06-30 14:53:28] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:53:28] local.INFO: Returning invoice details successfully  
[2025-06-30 14:53:28] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:53:42] local.INFO: Getting invoice details for ID: 179  
[2025-06-30 14:53:42] local.INFO: Invoice found: 179  
[2025-06-30 14:53:42] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:53:42] local.INFO: Returning invoice details successfully  
[2025-06-30 14:53:42] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:57:16] local.INFO: Getting invoice details for ID: 180  
[2025-06-30 14:57:17] local.INFO: Invoice found: 180  
[2025-06-30 14:57:17] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:57:17] local.INFO: Returning invoice details successfully  
[2025-06-30 14:57:17] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:57:30] local.INFO: Getting invoice details for ID: 180  
[2025-06-30 14:57:30] local.INFO: Invoice found: 180  
[2025-06-30 14:57:30] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:57:30] local.INFO: Returning invoice details successfully  
[2025-06-30 14:57:30] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 14:57:53] local.INFO: Getting invoice details for ID: 180  
[2025-06-30 14:57:53] local.INFO: Invoice found: 180  
[2025-06-30 14:57:53] local.INFO: Invoice has 1 transactions  
[2025-06-30 14:57:53] local.INFO: Returning invoice details successfully  
[2025-06-30 14:57:53] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:00:43] local.INFO: Getting invoice details for ID: 181  
[2025-06-30 15:00:43] local.INFO: Invoice found: 181  
[2025-06-30 15:00:43] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:00:43] local.INFO: Returning invoice details successfully  
[2025-06-30 15:00:43] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:00:50] local.INFO: Getting invoice details for ID: 181  
[2025-06-30 15:00:50] local.INFO: Invoice found: 181  
[2025-06-30 15:00:50] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:00:50] local.INFO: Returning invoice details successfully  
[2025-06-30 15:00:51] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:11:46] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:11:46] local.INFO: Invoice found: 184  
[2025-06-30 15:11:46] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:11:46] local.INFO: Returning invoice details successfully  
[2025-06-30 15:11:46] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:14:51] local.INFO: Getting invoice details for ID: 183  
[2025-06-30 15:14:51] local.ERROR: Error getting invoice details: No query results for model [App\Models\Invoice] 183  
[2025-06-30 15:14:51] local.ERROR: Invoice ID: 183  
[2025-06-30 15:14:51] local.ERROR: #0 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php(23): Illuminate\Database\Eloquent\Builder->findOrFail('183')
#1 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2372): Illuminate\Database\Eloquent\Model->forwardCallTo(Object(Illuminate\Database\Eloquent\Builder), 'findOrFail', Array)
#2 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2384): Illuminate\Database\Eloquent\Model->__call('findOrFail', Array)
#3 C:\xampp\htdocs\portalpwb\app\Http\Controllers\InvoiceController.php(139): Illuminate\Database\Eloquent\Model::__callStatic('findOrFail', Array)
#4 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(47): App\Http\Controllers\InvoiceController->getInvoiceDetails('183')
#5 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Route.php(266): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\InvoiceController), 'getInvoiceDetai...')
#6 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Route.php(212): Illuminate\Routing\Route->runController()
#7 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(808): Illuminate\Routing\Route->run()
#8 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(170): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#9 C:\xampp\htdocs\portalpwb\app\Http\Middleware\CheckSales.php(16): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): App\Http\Middleware\CheckSales->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(51): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(88): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#14 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#15 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#16 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#17 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#18 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#19 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(75): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(127): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(807): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(786): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(750): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#28 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(739): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#29 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(201): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#30 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(170): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#31 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(51): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#37 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(110): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(58): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(127): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#49 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(145): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#50 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1220): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#51 C:\xampp\htdocs\portalpwb\public\index.php(17): Illuminate\Foundation\Application->handleRequest(Object(Illuminate\Http\Request))
#52 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(23): require_once('C:\\xampp\\htdocs...')
#53 {main}  
[2025-06-30 15:14:58] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:14:58] local.INFO: Invoice found: 184  
[2025-06-30 15:14:58] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:14:58] local.INFO: Returning invoice details successfully  
[2025-06-30 15:14:58] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:15:19] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:15:19] local.INFO: Invoice found: 184  
[2025-06-30 15:15:19] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:15:19] local.INFO: Returning invoice details successfully  
[2025-06-30 15:15:19] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:15:30] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:15:30] local.INFO: Invoice found: 184  
[2025-06-30 15:15:30] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:15:30] local.INFO: Returning invoice details successfully  
[2025-06-30 15:15:30] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:15:53] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:15:53] local.INFO: Invoice found: 184  
[2025-06-30 15:15:54] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:15:54] local.INFO: Returning invoice details successfully  
[2025-06-30 15:15:54] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:16:01] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:16:01] local.INFO: Invoice found: 184  
[2025-06-30 15:16:01] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:16:01] local.INFO: Returning invoice details successfully  
[2025-06-30 15:16:01] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:16:03] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:16:03] local.INFO: Invoice found: 184  
[2025-06-30 15:16:03] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:16:03] local.INFO: Returning invoice details successfully  
[2025-06-30 15:16:03] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:16:09] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:16:09] local.INFO: Invoice found: 184  
[2025-06-30 15:16:09] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:16:09] local.INFO: Returning invoice details successfully  
[2025-06-30 15:16:09] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:22:23] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:22:23] local.INFO: Invoice found: 184  
[2025-06-30 15:22:23] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:22:23] local.INFO: Returning invoice details successfully  
[2025-06-30 15:22:23] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:24:21] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:24:21] local.INFO: Invoice found: 184  
[2025-06-30 15:24:21] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:24:21] local.INFO: Returning invoice details successfully  
[2025-06-30 15:24:21] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:27:25] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:27:25] local.INFO: Invoice found: 184  
[2025-06-30 15:27:25] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:27:25] local.INFO: Returning invoice details successfully  
[2025-06-30 15:27:25] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:27:31] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:27:31] local.INFO: Invoice found: 184  
[2025-06-30 15:27:31] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:27:31] local.INFO: Returning invoice details successfully  
[2025-06-30 15:27:31] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:28:12] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:28:12] local.INFO: Invoice found: 184  
[2025-06-30 15:28:12] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:28:12] local.INFO: Returning invoice details successfully  
[2025-06-30 15:28:12] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:29:42] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:29:42] local.INFO: Invoice found: 184  
[2025-06-30 15:29:42] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:29:42] local.INFO: Returning invoice details successfully  
[2025-06-30 15:29:42] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:31:01] local.INFO: Getting invoice details for ID: 184  
[2025-06-30 15:31:01] local.INFO: Invoice found: 184  
[2025-06-30 15:31:01] local.INFO: Invoice has 1 transactions  
[2025-06-30 15:31:01] local.INFO: Returning invoice details successfully  
[2025-06-30 15:31:01] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:40:05] local.INFO: New part created: tyrer20 - Tyre size 20.5 R25 with WHO inventory record  
[2025-06-30 15:42:16] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:42:48] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:43:03] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:43:04] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 15:43:10] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:00:21] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:02:45] local.INFO: New part created: jasatyre - Jasa tyre with WHO inventory record  
[2025-06-30 16:02:49] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:03:14] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:14:43] local.INFO: Unit data: {"id":205,"site_id":"TRB","unit_code":"oui","unit_type":"erer","nopr":null,"noqtn":null,"do_number":null,"noSPB":null,"created_at":"2025-06-30T08:14:40.000000Z","updated_at":"2025-06-30T08:14:40.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":464,"unit_id":205,"part_inventory_id":100196,"quantity":1,"price":5200000,"eum":"AE","created_at":"2025-06-30T08:14:40.000000Z","updated_at":"2025-06-30T08:14:40.000000Z","part_inventory":{"part_inventory_id":100196,"part_code":"C-SD-8126-PWB","site_part_name":null,"site_id":"TRB","priority":false,"date_priority":null,"min_stock":1,"max_stock":3,"oum":null,"stock_quantity":2,"created_at":"2025-06-21T16:41:24.000000Z","updated_at":"2025-06-23T09:08:57.000000Z","price":"5200000.00","item_code":null,"part":{"part_code":"C-SD-8126-PWB","part_name":"COMPRESSOR SD7H15 S8126 DOUBLE PULLEY A","bin_location":"-","part_type":"AC","price":5200000,"purchase_price":3000000,"eum":"EA","created_at":null,"updated_at":"2025-06-26T17:27:18.000000Z"}}}]}  
[2025-06-30 16:14:43] local.INFO: Parts data: [{"id":464,"unit_id":205,"part_inventory_id":100196,"quantity":1,"price":5200000,"eum":"AE","created_at":"2025-06-30T08:14:40.000000Z","updated_at":"2025-06-30T08:14:40.000000Z","part_inventory":{"part_inventory_id":100196,"part_code":"C-SD-8126-PWB","site_part_name":null,"site_id":"TRB","priority":false,"date_priority":null,"min_stock":1,"max_stock":3,"oum":null,"stock_quantity":2,"created_at":"2025-06-21T16:41:24.000000Z","updated_at":"2025-06-23T09:08:57.000000Z","price":"5200000.00","item_code":null,"part":{"part_code":"C-SD-8126-PWB","part_name":"COMPRESSOR SD7H15 S8126 DOUBLE PULLEY A","bin_location":"-","part_type":"AC","price":5200000,"purchase_price":3000000,"eum":"EA","created_at":null,"updated_at":"2025-06-26T17:27:18.000000Z"}}}]  
[2025-06-30 16:14:45] local.INFO: SPB Number:   
[2025-06-30 16:18:40] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-06-30 16:19:20] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:19:23] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:21:14] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:23:31] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:23:47] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:24:18] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:25:18] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:25:28] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:25:40] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:06] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:08] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:08] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:09] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:09] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:10] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:39] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:42] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:43] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-28  
[2025-06-30 16:26:52] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:52] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:26:53] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:27:11] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:27:12] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:27:13] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:27:13] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:27:13] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:27:51] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:28:04] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:29:00] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:29:37] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:30:40] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:31:41] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:31:43] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:32:05] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:32:07] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:32:10] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-28  
[2025-06-30 16:32:31] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:32:48] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:33:15] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:33:34] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:38:48] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:38:58] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:39:55] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:39:59] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:42:25] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:42:31] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:43:20] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:43:21] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:43:22] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:43:22] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:43:25] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:44:06] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:47:42] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:50:44] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:50:54] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'part_name' cannot be null (Connection: mysql, SQL: update `manual_invoice_parts` set `part_name` = ? where `id` = 158) {"userId":"000012","exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'part_name' cannot be null (Connection: mysql, SQL: update `manual_invoice_parts` set `part_name` = ? where `id` = 158) at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('update `manual_...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(584): Illuminate\\Database\\Connection->run('update `manual_...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(536): Illuminate\\Database\\Connection->affectingStatement('update `manual_...', Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3830): Illuminate\\Database\\Connection->update('update `manual_...', Array)
#4 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sales\\SalesDashboardController.php(969): Illuminate\\Database\\Query\\Builder->update(Object(Illuminate\\Support\\Collection))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Sales\\SalesDashboardController->updatePartValueinvoice(Object(Illuminate\\Http\\Request), '158')
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Sales\\SalesDashboardController), 'updatePartValue...')
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckSales.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckSales->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#54 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'part_name' cannot be null at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:596)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(596): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('update `manual_...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('update `manual_...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(584): Illuminate\\Database\\Connection->run('update `manual_...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(536): Illuminate\\Database\\Connection->affectingStatement('update `manual_...', Array)
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3830): Illuminate\\Database\\Connection->update('update `manual_...', Array)
#6 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sales\\SalesDashboardController.php(969): Illuminate\\Database\\Query\\Builder->update(Object(Illuminate\\Support\\Collection))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Sales\\SalesDashboardController->updatePartValueinvoice(Object(Illuminate\\Http\\Request), '158')
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Sales\\SalesDashboardController), 'updatePartValue...')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckSales.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckSales->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}
"} 
[2025-06-30 16:51:25] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:52:05] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:52:23] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:52:25] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:52:52] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:53:39] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:54:48] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:55:12] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:56:17] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:56:37] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:57:39] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:57:53] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:57:58] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 16:58:16] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 17:00:18] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-06-30 17:00:28] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:08:14] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-01 08:09:26] local.INFO: Getting invoice details for ID: 176  
[2025-07-01 08:09:26] local.INFO: Invoice found: 176  
[2025-07-01 08:09:26] local.INFO: Invoice has 1 transactions  
[2025-07-01 08:09:26] local.INFO: Returning invoice details successfully  
[2025-07-01 08:09:26] local.INFO: Raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-01 08:10:52] local.INFO: Getting invoice details for ID: 176  
[2025-07-01 08:10:52] local.INFO: Invoice found: 176  
[2025-07-01 08:10:52] local.INFO: Invoice has 1 transactions  
[2025-07-01 08:10:52] local.INFO: Returning invoice details successfully  
[2025-07-01 08:10:52] local.INFO: Raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-01 08:11:25] local.INFO: Getting invoice details for ID: 176  
[2025-07-01 08:11:25] local.INFO: Invoice found: 176  
[2025-07-01 08:11:25] local.INFO: Invoice has 1 transactions  
[2025-07-01 08:11:25] local.INFO: Returning invoice details successfully  
[2025-07-01 08:11:25] local.INFO: Raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-01 08:11:44] local.INFO: Getting invoice details for ID: 176  
[2025-07-01 08:11:44] local.INFO: Invoice found: 176  
[2025-07-01 08:11:44] local.INFO: Invoice has 1 transactions  
[2025-07-01 08:11:44] local.INFO: Returning invoice details successfully  
[2025-07-01 08:11:44] local.INFO: Raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-01 08:13:46] local.INFO: Getting invoice details for ID: 184  
[2025-07-01 08:13:46] local.INFO: Invoice found: 184  
[2025-07-01 08:13:46] local.INFO: Invoice has 1 transactions  
[2025-07-01 08:13:46] local.INFO: Returning invoice details successfully  
[2025-07-01 08:13:46] local.INFO: Raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:13:50] local.INFO: Getting invoice details for ID: 187  
[2025-07-01 08:13:50] local.INFO: Invoice found: 187  
[2025-07-01 08:13:50] local.INFO: Invoice has 1 transactions  
[2025-07-01 08:13:50] local.INFO: Returning invoice details successfully  
[2025-07-01 08:13:50] local.INFO: Raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-01 08:24:22] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:24:39] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:30:34] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:32:44] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:34:46] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:35:56] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:39:05] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:39:41] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:40:02] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:40:40] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:43:23] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 08:44:28] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:04:12] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:06:10] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:06:25] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:06:51] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:07:03] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:07:14] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:10:26] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:10:55] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:15:09] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:15:20] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:15:29] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:15:45] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:15:52] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:16:26] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:16:58] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:17:26] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:17:28] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:17:59] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:18:06] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:18:23] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
[2025-07-01 09:33:58] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-06-30  
