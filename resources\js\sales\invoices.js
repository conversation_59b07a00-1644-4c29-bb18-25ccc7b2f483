import { all } from "axios";

document.addEventListener("DOMContentLoaded", function () {
    const lengthPagination = document.getElementById("lengthpagenation");
    if (lengthPagination) {
        lengthPagination.addEventListener("change", function () {
            itemsPerPage = parseInt(this.value);

            currentPage = 1; // Reset to first page when changing items per page
            loadInvoices();
        });
    }

    // Add event listener for document modal close button
    document.querySelectorAll('[data-bs-dismiss="modal"]').forEach((button) => {
        button.addEventListener("click", function () {
            const modalId = this.closest(".modal").id;
            const modalElement = document.getElementById(modalId);
            const modalInstance = bootstrap.Modal.getInstance(modalElement);

            // If bootstrap.Modal.getInstance is not available, use jQuery or manual close
            if (modalInstance) {
                modalInstance.hide();
            } else {
                // Fallback to manual closing
                modalElement.classList.remove("show");
                modalElement.style.display = "none";
                document.body.classList.remove("modal-open");
                const backdrop = document.querySelector(".modal-backdrop");
                if (backdrop) {
                    backdrop.remove();
                }
            }
        });
    });

    // Add Direct Invoice button click event - only if the button exists
    const addDirectInvoiceBtn = document.getElementById(
        "add-direct-invoice-btn"
    );
    if (addDirectInvoiceBtn) {
        addDirectInvoiceBtn.addEventListener("click", function () {
            // Reset form
            document.getElementById("direct-invoice-form").reset();

            // Reset invoice ID and change modal title to "Add"
            document.getElementById("invoice_id").value = "";
            document.getElementById("direct-invoice-modal-label").textContent =
                "Tambah Invoice Manual";

            // Set default date values using timezone-safe methods
            const formattedToday = window.DateUtils
                ? window.DateUtils.getTodayFormatted()
                : new Date().toISOString().split("T")[0];
            document.getElementById("tanggal_invoice").value = formattedToday;

            // Calculate due date (60 days from today) using timezone-safe methods
            const formattedDueDate = window.DateUtils
                ? window.DateUtils.getDateFromToday(60)
                : (() => {
                      const dueDate = new Date();
                      dueDate.setDate(dueDate.getDate() + 60);
                      return dueDate.toISOString().split("T")[0];
                  })();
            document.getElementById("due_date").value = formattedDueDate;

            // Set default values for hidden fields
            document.getElementById("subtotal").value = "0";
            document.getElementById("ppn").value = "11";
            document.getElementById("unit").value = "Manual";

            // Add event listener for invoice date change to automatically update due date
            document
                .getElementById("tanggal_invoice")
                .addEventListener("change", function () {
                    if (this.value) {
                        // Calculate due date (60 days from invoice date) using timezone-safe methods
                        const formattedDueDate = window.DateUtils
                            ? window.DateUtils.addDaysToDate(this.value, 60)
                            : (() => {
                                  const invoiceDate = new Date(this.value);
                                  if (!isNaN(invoiceDate.getTime())) {
                                      const dueDate = new Date(invoiceDate);
                                      dueDate.setDate(dueDate.getDate() + 60);
                                      return dueDate
                                          .toISOString()
                                          .split("T")[0];
                                  }
                                  return "";
                              })();

                        if (formattedDueDate) {
                            document.getElementById("due_date").value =
                                formattedDueDate;
                        }
                    }
                });

            // Show the modal
            const modal = document.getElementById("direct-invoice-modal");
            modal.classList.add("show");
            modal.style.display = "block";
            document.body.classList.add("modal-open");

            // Add backdrop if it doesn't exist
            if (!document.querySelector(".modal-backdrop")) {
                const backdrop = document.createElement("div");
                backdrop.className = "modal-backdrop fade show";
                document.body.appendChild(backdrop);
            }
        });
    }
    // Function to toggle dropdown
    function toggleDropdown(e) {
        e.preventDefault();
        e.stopPropagation();

        const dropdownBtn = this;
        const dropdownMenu = dropdownBtn.nextElementSibling;

        // Close all other open dropdowns first
        document.querySelectorAll(".dropdown-menu.show").forEach((menu) => {
            if (menu !== dropdownMenu) {
                menu.classList.remove("show");
                menu.previousElementSibling.setAttribute(
                    "aria-expanded",
                    "false"
                );
            }
        });

        // Toggle this dropdown
        dropdownMenu.classList.toggle("show");

        // Update aria-expanded attribute
        const isExpanded = dropdownMenu.classList.contains("show");
        dropdownBtn.setAttribute(
            "aria-expanded",
            isExpanded ? "true" : "false"
        );

        // If dropdown is now open, add a one-time click event to close when clicking outside
        if (isExpanded) {
            setTimeout(() => {
                const closeDropdownOnClickOutside = function (event) {
                    if (
                        !dropdownMenu.contains(event.target) &&
                        !dropdownBtn.contains(event.target)
                    ) {
                        dropdownMenu.classList.remove("show");
                        dropdownBtn.setAttribute("aria-expanded", "false");
                        document.removeEventListener(
                            "click",
                            closeDropdownOnClickOutside
                        );
                    }
                };
                document.addEventListener("click", closeDropdownOnClickOutside);
            }, 0);
        }
    }

    // Helper function to get PO number from invoice
    function getPONumberFromInvoice(invoice) {
        // Check if invoice has unit_transactions array and it has at least one entry with a po_number
        if (
            invoice.unit_transactions &&
            invoice.unit_transactions.length > 0 &&
            invoice.unit_transactions[0].po_number
        ) {
            return invoice.unit_transactions[0].po_number;
        }

        // Fallback to invoice's po_number if available
        if (invoice.po_number) {
            return invoice.po_number;
        }

        // Default value if no PO number is found
        return "-";
    }

    // Global click handler for status options
    document.addEventListener("click", function (e) {
        // Handle status option clicks
        if (e.target && e.target.classList.contains("status-option")) {
            e.preventDefault();
            e.stopPropagation();

            // Close the dropdown
            const dropdownMenu = e.target.closest(".dropdown-menu");
            if (dropdownMenu) {
                dropdownMenu.classList.remove("show");
                const dropdownBtn = dropdownMenu.previousElementSibling;
                if (dropdownBtn) {
                    dropdownBtn.setAttribute("aria-expanded", "false");
                }
            }

            // Update the status
            const invoiceId = e.target.getAttribute("data-id");
            const newStatus = e.target.getAttribute("data-status");
            updateInvoiceStatus(invoiceId, newStatus);
        }
    });
    // Set default date range (current month)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    // Set default date range in the date inputs
    document.getElementById("date-from").valueAsDate = firstDay;
    document.getElementById("date-to").valueAsDate = today;

    // Variables for pagination
    let currentPage = 1;
    let totalPages = 1;
    let itemsPerPage = lengthPagination.value;

    let sortField = "no_invoice";
    let sortDirection = "asc";
    let searchTerm = "";

    // Store all loaded invoices for client-side filtering
    let allLoadedInvoices = [];
    let filteredInvoices = [];

    // Load invoices on page load
    loadInvoices();

    // Date input change events - automatically reload data when dates change
    const dateFromInput = document.getElementById("date-from");
    const dateToInput = document.getElementById("date-to");
    const customerbtn = document.getElementById("customer");

    customerbtn.addEventListener("change", function () {
        currentPage = 1;
        // Show loading indicator
        Swal.fire({
            title: "Memuat Data",
            text: "Mengambil data sesuai tanggal...",
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            },
            timer: 1000,
            timerProgressBar: true,
        });
        loadInvoices();
    });

    if (dateFromInput) {
        dateFromInput.addEventListener("change", function () {
            // Reset page to 1
            currentPage = 1;

            // Show loading indicator
            Swal.fire({
                title: "Memuat Data",
                text: "Mengambil data sesuai tanggal...",
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                },
                timer: 1000,
                timerProgressBar: true,
            });
            // Reload data from server with new date range
            loadInvoices();
        });
    } else {
        console.warn('Element with ID "date-from" not found');
    }

    if (dateToInput) {
        dateToInput.addEventListener("change", function () {
            // Reset page to 1
            currentPage = 1;

            // Show loading indicator
            Swal.fire({
                title: "Memuat Data",
                text: "Mengambil data sesuai tanggal...",
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                },
                timer: 1000,
                timerProgressBar: true,
            });

            // Reload data from server with new date range
            loadInvoices();
        });
    } else {
        console.warn('Element with ID "date-to" not found');
    }

    // Search input event - client-side filtering
    const searchInput = document.getElementById("search-input");
    if (searchInput) {
        searchInput.addEventListener("input", function () {
            searchTerm = this.value.toLowerCase().trim();
            currentPage = 1;
            loadInvoices();
        });
    }
    const sortableHeaders = document.querySelectorAll(".sortable");
    if (sortableHeaders.length > 0) {
        sortableHeaders.forEach((header) => {
            header.addEventListener("click", function () {
                const field = this.getAttribute("data-sort");

                // Toggle sort direction if clicking on the same field
                if (field === sortField) {
                    sortDirection = sortDirection === "asc" ? "desc" : "asc";
                } else {
                    sortField = field;
                    sortDirection = "asc";
                }

                // Update UI to show sort direction
                document.querySelectorAll(".sortable").forEach((h) => {
                    h.classList.remove("asc", "desc");
                });
                this.classList.add(sortDirection);

                // Always sort the data client-side for immediate feedback
                if (allLoadedInvoices.length > 0) {
                    sortInvoicesClientSide();
                    filterAndDisplayInvoices();
                } else {
                    // If no data is loaded yet, load from server
                    loadInvoices();
                }
            });
        });
    }
    document.addEventListener("click", function (e) {
        if (e.target && e.target.closest(".btn-view-document")) {
            const button = e.target.closest(".btn-view-document");
            const documentPath = button.getAttribute("data-path");
            if (documentPath) {
                showDocumentInModal(documentPath);
            }
        }
    });

    // Pindahkan bagian ini ke atas file, di luar fungsi loadInvoices()
    let lastDateFrom = "";
    let lastDateTo = "";

    // Fungsi untuk update dropdown customer
    function updateCustomerDropdown(customers, selectedCustomer = "") {
        const customerbtn = document.getElementById("customer");
        const currentValue = customerbtn.value; // Simpan nilai yang sedang dipilih

        customerbtn.innerHTML = "";
        const option = document.createElement("option");
        option.value = "";
        option.textContent = "Semua Customer";
        customerbtn.appendChild(option);

        customers.forEach((customer) => {
            const option = document.createElement("option");
            option.value = customer;
            option.textContent = customer;
            if (customer === selectedCustomer) {
                option.selected = true;
            }
            customerbtn.appendChild(option);
        });

        // Kembalikan nilai yang dipilih sebelumnya jika ada
        if (selectedCustomer) {
            customerbtn.value = selectedCustomer;
        } else if (currentValue && customers.includes(currentValue)) {
            customerbtn.value = currentValue;
        }
    }

    // Function to load invoices
    function loadInvoices() {
        // Get date range values
        let dateFrom = document.getElementById("date-from").value;
        let dateTo = document.getElementById("date-to").value;
        let customerbtn = document.getElementById("customer").value;

        // If dates are not set, use default (first day of current month to today)
        if (!dateFrom || !dateTo) {
            // Use global date utilities if available, otherwise fallback
            if (window.DateUtils) {
                dateFrom = window.DateUtils.getFirstDayOfMonth();
                dateTo = window.DateUtils.getTodayFormatted();
            } else {
                const today = new Date();
                const firstDay = new Date(
                    today.getFullYear(),
                    today.getMonth(),
                    1
                );
                dateFrom = firstDay.toISOString().split("T")[0];
                dateTo = today.toISOString().split("T")[0];
            }

            // Update the date inputs
            document.getElementById("date-from").value = dateFrom;
            document.getElementById("date-to").value = dateTo;
        }

        // Format dates for display
        const formatDateForDisplay = (dateStr) => {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                return dateStr;
            }
            return date.toLocaleDateString("id-ID", {
                day: "numeric",
                month: "long",
                year: "numeric",
            });
        };

        const fromDateDisplay = formatDateForDisplay(dateFrom);
        const toDateDisplay = formatDateForDisplay(dateTo);

        // Ensure dates are in YYYY-MM-DD format using timezone-safe method
        const formatDate = (dateStr) => {
            // Use global date utilities if available
            if (window.DateUtils) {
                return window.DateUtils.formatDateForInput(dateStr);
            }

            // Fallback to original method
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                return dateStr; // Return as is if invalid
            }
            return date.toISOString().split("T")[0]; // YYYY-MM-DD format
        };

        dateFrom = formatDate(dateFrom);
        dateTo = formatDate(dateTo);

        // Show loading state
        const tableBody = document.getElementById("invoices-table-body");
        tableBody.innerHTML = `
            <tr>
                <td colspan="16" class="text-center">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <div class="spinner-border text-primary mb-2" role="status">
                            <span class="sr-only">.</span>
                        </div>
                        <p class="mb-0">Memuat data invoice...</p>
                        <p class="small text-muted">Periode: ${fromDateDisplay} - ${toDateDisplay}</p>
                    </div>
                </td>
            </tr>
        `;

        // Build query parameters
        const params = new URLSearchParams({
            page: currentPage,
            per_page: itemsPerPage,
            sort_field: sortField,
            sort_direction: sortDirection,
            date_from: dateFrom,
            date_to: dateTo,
            customer: customerbtn,
            search: searchTerm,
            all: "true", // Mark this as a request for all invoices
            include_all_types: "true", // Include all types of invoices (penawaran, unit, manual)
            show_all: "true", // Show all invoices regardless of status
        });
        fetch(`/sales/all-invoices-data?${params.toString()}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then((data) => {
                const formattedtotalinvoice = new Intl.NumberFormat("id-ID", {
                    style: "currency",
                    currency: "IDR",
                    minimumFractionDigits: 0,
                }).format(data.totalinvoice || 0);

                document.getElementById("totalinvoice").innerText =
                    formattedtotalinvoice;

                allLoadedInvoices = data.data || [];
                filteredInvoices = [...allLoadedInvoices];

                // Check if we have data
                if (data.data && data.data.length > 0) {
                    const currentDateFrom =
                        document.getElementById("date-from").value;
                    const currentDateTo =
                        document.getElementById("date-to").value;

                    if (
                        currentDateFrom !== lastDateFrom ||
                        currentDateTo !== lastDateTo
                    ) {
                        // Tanggal berubah, update dropdown customer
                        updateCustomerDropdown(
                            data.customer,
                            customerbtn.value
                        );
                        lastDateFrom = currentDateFrom;
                        lastDateTo = currentDateTo;
                    }

                    displayInvoices(data.data);
                } else {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="16" class="text-center">
                                <div class="text-center p-3">
                                    <img src="/assets/images/no-data.svg" alt="No Data" style="max-width: 150px;">
                                    <p class="mt-2">Tidak ada invoice dalam periode ini</p>
                                    <p class="small text-muted">Periode: ${fromDateDisplay} sampai ${toDateDisplay}</p>
                                    <div class="mt-2">
                                        <button id="debug-btn" class="btn btn-sm btn-outline-secondary">Cek Database</button>
                                        <button id="reload-btn" class="btn btn-sm btn-primary ml-2">Muat Ulang</button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    `;

                    // Add event listener to debug button
                    document
                        .getElementById("debug-btn")
                        ?.addEventListener("click", function () {
                            // Show a message to check the database
                            Swal.fire({
                                title: "Informasi Debug",
                                html: `
                                <p>Tidak ada data invoice yang ditemukan untuk periode:</p>
                                <p><strong>Dari:</strong> ${fromDateDisplay} <strong>Sampai:</strong> ${toDateDisplay}</p>
                                <p>Pastikan data invoice tersedia di database dengan tanggal invoice dalam rentang tersebut.</p>
                                <p class="text-muted small">Endpoint: /sales/all-invoices-data</p>
                                <p class="text-muted small">Parameters: ${params.toString()}</p>
                            `,
                                icon: "info",
                            });
                        });

                    // Add event listener to reload button
                    document
                        .getElementById("reload-btn")
                        ?.addEventListener("click", function () {
                            // Show loading indicator
                            Swal.fire({
                                title: "Memuat Ulang Data",
                                text: "Mohon tunggu...",
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                },
                            });

                            // Reload data
                            loadInvoices();

                            // Close loading indicator after a short delay
                            setTimeout(() => {
                                Swal.close();
                            }, 1000);
                        });
                }

                updatePagination(data);
                updateShowingText(data);
            })
            .catch((error) => {
                console.error("Error fetching invoices:", error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="16" class="text-center">
                            <div class="text-center p-3">
                                <p class="text-danger">Error loading data: ${error.message}</p>
                                <p>Silakan coba lagi atau periksa konsol untuk detail error.</p>
                                <div class="mt-3">
                                    <button id="error-details-btn" class="btn btn-sm btn-outline-danger">Lihat Detail Error</button>
                                    <button id="retry-btn" class="btn btn-sm btn-primary ml-2">Coba Lagi</button>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;

                // Add event listener to error details button
                document
                    .getElementById("error-details-btn")
                    ?.addEventListener("click", function () {
                        Swal.fire({
                            title: "Detail Error",
                            html: `
                            <div class="text-left">
                                <p><strong>Error Message:</strong> ${
                                    error.message
                                }</p>
                                <p><strong>Endpoint:</strong> /sales/all-invoices-data</p>
                                <p><strong>Parameters:</strong> ${params.toString()}</p>
                                <p><strong>Date Range:</strong> ${fromDateDisplay} to ${toDateDisplay}</p>
                                <hr>
                                <p>Kemungkinan penyebab:</p>
                                <ul>
                                    <li>Endpoint tidak ditemukan (404)</li>
                                    <li>Format tanggal tidak sesuai</li>
                                    <li>Tidak ada data invoice dalam rentang tanggal</li>
                                    <li>Masalah pada server</li>
                                </ul>
                            </div>
                        `,
                            icon: "error",
                            confirmButtonText: "Tutup",
                        });
                    });

                // Add event listener to retry button
                document
                    .getElementById("retry-btn")
                    ?.addEventListener("click", function () {
                        loadInvoices();
                    });
            });
    }

    // Function to display invoices in the table

    function displayInvoices(invoices) {
        const tableBody = document.getElementById("invoices-table-body");
        tableBody.innerHTML = "";

        if (invoices.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="16" class="text-center">
                        <div class="text-center p-3">
                            <img src="/assets/images/no-data.svg" alt="No Data" style="max-width: 150px;">
                            <p class="mt-2">Tidak ada invoice dalam periode ini</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        invoices.forEach((invoice, index) => {
            // Format dates
            const invoiceDate = invoice.tanggal_invoice
                ? new Date(invoice.tanggal_invoice)
                : null;
            const formattedInvoiceDate = invoiceDate
                ? invoiceDate.toLocaleDateString("id-ID", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                  })
                : "-";

            const dueDate = invoice.due_date
                ? new Date(invoice.due_date)
                : null;
            const formattedDueDate = dueDate
                ? dueDate.toLocaleDateString("id-ID", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                  })
                : "-";

            const unitsOutDate = invoice.units_out_date
                ? new Date(invoice.units_out_date)
                : null;
            const formattedUnitsOutDate = unitsOutDate
                ? unitsOutDate.toLocaleDateString("id-ID", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                  })
                : "-";

            // Get payment status class
            let statusClass = "bg-secondary";
            let statusText = invoice.payment_status ?? "";

            if (invoice.payment_status === "Lunas") {
                statusClass = "bg-success";
                statusText = "Lunas";
            } else if (invoice.payment_status === "Jatuh Tempo") {
                statusClass = "bg-danger";
                statusText = "Jatuh Tempo";
            } else if (invoice.payment_status === "Draf") {
                statusClass = "bg-primary";
                statusText = "Draf/Keep Tanggal";
            } else if (invoice.payment_status === "Belum Lunas") {
                statusClass = "bg-warning";
                statusText = "Belum Lunas";
            }

            // Check if invoice is past due date
            let dueStatus = '<span class="badge bg-info">Belum Tempo</span>';
            if (invoice.due_date) {
                const today = new Date();
                const dueDate = new Date(invoice.due_date);

                if (dueDate < today && invoice.payment_status !== "Lunas") {
                    dueStatus =
                        '<span class="badge bg-danger">Jatuh Tempo</span>';
                }
            }

            // Check if this is a manual invoice
            const isManualInvoice =
                invoice.unit === "Manual" || invoice.unit_list === "Manual";

            // Calculate termin (payment term in days)
            let termin = 60; // Default 60 days
            if (invoiceDate && dueDate) {
                // Calculate the difference in days
                const diffTime = Math.abs(dueDate - invoiceDate);
                termin = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            }

            // Get the PO number using the helper function
            const poNumber = getPONumberFromInvoice(invoice);

            // Format currency values
            const formattedSubtotal = new Intl.NumberFormat("id-ID", {
                style: "currency",
                currency: "IDR",
                minimumFractionDigits: 0,
            }).format(invoice.subtotal || 0);

            const formattedTax = new Intl.NumberFormat("id-ID", {
                style: "currency",
                currency: "IDR",
                minimumFractionDigits: 0,
            }).format(invoice.tax_amount || 0);

            const formattedTotal = new Intl.NumberFormat("id-ID", {
                style: "currency",
                currency: "IDR",
                minimumFractionDigits: 0,
            }).format(invoice.total_amount || 0);

            // Extract only the number part from no_invoice (before the first slash)
            const invoiceNumberOnly = invoice.no_invoice
                ? invoice.no_invoice.split("/")[0]
                : "-";

            const row = document.createElement("tr");
            row.innerHTML = `
                <td>${formattedInvoiceDate}</td>
                <td>${termin} hari</td>
                <td>${formattedDueDate}</td>
                <td>
                    ${
                        invoice.document_path || invoice.signed_document_path
                            ? `<a href="#" class="document-link" data-path="${
                                  invoice.signed_document_path ||
                                  invoice.document_path
                              }">${invoiceNumberOnly}</a>`
                            : `${invoiceNumberOnly}`
                    }
                    ${
                        isManualInvoice
                            ? '<span class="badge bg-info ms-1">Manual</span>'
                            : ""
                    }
                    ${
                        invoice.signed_document_path
                            ? '<span class="badge bg-success ms-1">Signed</span>'
                            : ""
                    }
                </td>
                <td>${invoice.customer || "-"}</td>
                <td>${invoice.type || "-"}</td>
                <td>${poNumber}</td>
                <td>${invoice.sn || "-"}</td>
                <td>${invoice.trouble || "-"}</td>
                <td>${formattedSubtotal}</td>
                <td>${formattedTax}</td>
                <td>${formattedTotal}</td>
                <td>
                    <div class="dropdown status-dropdown">
                        <button class="btn btn-sm ${
                            statusClass ?? "btn-warning"
                        } dropdown-toggle status-dropdown-btn" type="button" data-id="${
                invoice.id
            }" aria-expanded="false">${statusText}
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item status-option ${
                                statusText === "Lunas" ? "active" : ""
                            }" href="#" data-id="${
                invoice.id
            }" data-status="Lunas">Lunas</a>
                            <a class="dropdown-item status-option ${
                                statusText === "Belum Lunas" ? "active" : ""
                            }" href="#" data-id="${
                invoice.id
            }" data-status="Belum Lunas">Belum Lunas</a>
                            <a class="dropdown-item status-option ${
                                statusText === "Draf" ? "active" : ""
                            }" href="#" data-id="${
                invoice.id
            }" data-status="Draf">Draf/Pendingkan</a>
                        </div>
                    </div>
                </td>
                <td>
                    ${
                        invoice.document_path || invoice.signed_document_path
                            ? `<div class="btn-group">
                            <a href="/assets/invoice_documents/${
                                invoice.signed_document_path ||
                                invoice.document_path
                            }" class="btn btn-sm btn-success" target="_blank" download>
                                <i class="mdi mdi-download"></i>
                            </a>
                        </div>`
                            : '<span class="badge bg-secondary"><i class="mdi mdi-file-remove"></i></span>'
                    }
                </td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-primary edit-invoice-btn" data-id="${
                            invoice.id
                        }">
                            <i class="mdi mdi-pencil"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-danger delete-invoice-btn" data-id="${
                            invoice.id
                        }">
                            <i class="mdi mdi-delete"></i> Hapus
                        </button>
                    </div>
                </td>
            `;

            tableBody.appendChild(row);
        });

        // Add event listeners to document links
        document.querySelectorAll(".document-link").forEach((link) => {
            link.addEventListener("click", function (e) {
                e.preventDefault();
                const documentPath = this.getAttribute("data-path");
                showDocumentInModal(documentPath);
            });
        });

        // Add event listeners to status dropdown options
        document.querySelectorAll(".status-option").forEach((option) => {
            option.addEventListener("click", function (e) {
                e.preventDefault();
                const invoiceId = this.getAttribute("data-id");
                const newStatus = this.getAttribute("data-status");
                updateInvoiceStatus(invoiceId, newStatus);
            });
        });

        // Add click event listeners to dropdown buttons
        document.querySelectorAll(".status-dropdown-btn").forEach((button) => {
            // Remove existing click listeners
            button.removeEventListener("click", toggleDropdown);

            // Add new click listener
            button.addEventListener("click", toggleDropdown);
        });

        // Add event listeners to edit buttons
        document.querySelectorAll(".edit-invoice-btn").forEach((button) => {
            button.addEventListener("click", function () {
                const invoiceId = this.getAttribute("data-id");
                editInvoice(invoiceId);
            });
        });

        // Add event listeners to delete buttons
        document.querySelectorAll(".delete-invoice-btn").forEach((button) => {
            button.addEventListener("click", function () {
                const invoiceId = this.getAttribute("data-id");
                deleteInvoice(invoiceId);
            });
        });
    }

    // Function to print invoice
    function printInvoice(invoiceId) {
        // Open invoice in new window for printing
        window.open(`/sales/invoices/${invoiceId}/download`, "_blank");
    }

    // Function to show invoice details in modal (kept for reference but not used)
    function showInvoiceDetails(invoiceId) {
        const modal = new bootstrap.Modal(
            document.getElementById("invoice-detail-modal")
        );
        const modalContent = document.getElementById("invoice-detail-content");

        // Show loading state
        modalContent.innerHTML = `
            <div class="d-flex justify-content-center p-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">.</span>
                </div>
            </div>
        `;

        // Show modal
        modal.show();

        // Fetch invoice details
        fetch(`/sales/invoices/${invoiceId}`)
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    displayInvoiceDetails(data.invoice);
                } else {
                    modalContent.innerHTML = `
                        <div class="alert alert-danger">
                            Error loading invoice details: ${data.message}
                        </div>
                    `;
                }
            })
            .catch((error) => {
                modalContent.innerHTML = `
                    <div class="alert alert-danger">
                        Error loading invoice details. Please try again.
                    </div>
                `;
            });
    }

    // Function to display invoice details in modal
    function displayInvoiceDetails(invoice) {
        const modalContent = document.getElementById("invoice-detail-content");

        // Format dates
        const invoiceDate = invoice.tanggal_invoice
            ? new Date(invoice.tanggal_invoice)
            : null;
        const formattedDate = invoiceDate
            ? invoiceDate.toLocaleDateString("id-ID", {
                  day: "numeric",
                  month: "long",
                  year: "numeric",
              })
            : "-";

        const dueDate = invoice.due_date ? new Date(invoice.due_date) : null;
        const formattedDueDate = dueDate
            ? dueDate.toLocaleDateString("id-ID", {
                  day: "numeric",
                  month: "long",
                  year: "numeric",
              })
            : "-";

        const paymentDate = invoice.payment_date
            ? new Date(invoice.payment_date)
            : null;
        const formattedPaymentDate = paymentDate
            ? paymentDate.toLocaleDateString("id-ID", {
                  day: "numeric",
                  month: "long",
                  year: "numeric",
              })
            : "-";

        // Format amounts
        const formattedSubtotal = new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
        }).format(invoice.subtotal || 0);

        const formattedTax = new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
        }).format(invoice.tax_amount || 0);

        const formattedTotal = new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
        }).format(invoice.total_amount || 0);

        // Get payment status class
        let statusClass = "bg-warning";
        if (invoice.payment_status === "Lunas") {
            statusClass = "bg-success";
        } else if (invoice.payment_status === "Jatuh Tempo") {
            statusClass = "bg-danger";
        } else if (invoice.payment_status === "Draf") {
            statusClass = "bg-secondary";
        }

        // Build HTML for invoice details
        modalContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Nomor Invoice:</strong> ${
                        invoice.no_invoice || "-"
                    }</p>
                    <p><strong>Tanggal Invoice:</strong> ${formattedDate}</p>
                    <p><strong>Tanggal Jatuh Tempo:</strong> ${formattedDueDate}</p>
                    <p><strong>Customer:</strong> ${invoice.customer || "-"}</p>
                    <p><strong>Site:</strong> ${invoice.site_name || "-"}</p>
                    <p><strong>Lokasi:</strong> ${
                        invoice.location || invoice.lokasi || "-"
                    }</p>
                    <p><strong>Nomor PO:</strong> ${getPONumberFromInvoice(
                        invoice
                    )}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Status Pembayaran:</strong> <span class="badge ${statusClass}">${
            invoice.payment_status || "Belum Lunas"
        }</span></p>
                    <p><strong>Tanggal Pembayaran:</strong> ${formattedPaymentDate}</p>
                    <p><strong>Catatan Pembayaran:</strong> ${
                        invoice.payment_notes || "-"
                    }</p>
                    <p><strong>Serial Number:</strong> ${invoice.sn || "-"}</p>
                    <p><strong>Kategori:</strong> ${invoice.trouble || "-"}</p>
                </div>
            </div>

            <hr>

            <h6>Detail Transaksi</h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead class="bg-light">
                        <tr>
                            <th>Unit</th>
                            <th>Deskripsi</th>
                            <th>Tanggal</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${
                            invoice.unit_transactions &&
                            invoice.unit_transactions.length > 0
                                ? invoice.unit_transactions
                                      .map(
                                          (transaction) => `
                                <tr>
                                    <td>${
                                        transaction.unit
                                            ? transaction.unit.unit_code
                                            : "-"
                                    }</td>
                                    <td>${transaction.description || "-"}</td>
                                    <td>${
                                        transaction.created_at
                                            ? new Date(
                                                  transaction.created_at
                                              ).toLocaleDateString("id-ID")
                                            : "-"
                                    }</td>
                                </tr>
                            `
                                      )
                                      .join("")
                                : '<tr><td colspan="3" class="text-center">Tidak ada data transaksi</td></tr>'
                        }
                    </tbody>
                </table>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    ${
                        invoice.document_path || invoice.signed_document_path
                            ? `<p><strong>Dokumen Invoice:</strong> <button class="btn btn-sm btn-primary btn-view-document" data-path="${
                                  invoice.signed_document_path ||
                                  invoice.document_path
                              }"><i class="mdi mdi-file-document"></i> Lihat Lampiran</button>
                        ${
                            invoice.signed_document_path
                                ? '<span class="badge bg-success ms-2">Signed</span>'
                                : ""
                        }
                        </p>`
                            : '<p><strong>Dokumen Invoice:</strong> <span class="badge bg-secondary">Tidak ada lampiran</span></p>'
                    }
                </div>
                <div class="col-md-6 text-right">
                    <p><strong>Subtotal:</strong> ${formattedSubtotal}</p>
                    <p><strong>PPN (${(invoice.ppn * 100).toFixed(
                        0
                    )}%):</strong> ${formattedTax}</p>
                    <p><strong>Total:</strong> ${formattedTotal}</p>
                </div>
            </div>
        `;
    }

    // Function to update pagination for server-side data
    function updatePagination(data) {
        const pagination = document.getElementById("pagination");
        pagination.innerHTML = "";

        totalPages = data.last_page;
        currentPage = data.current_page;

        // Previous button
        const prevLi = document.createElement("li");
        prevLi.className = `page-item ${currentPage === 1 ? "disabled" : ""}`;
        prevLi.innerHTML = `<a class="page-link" href="#" data-page="${
            currentPage - 1
        }">&laquo;</a>`;
        pagination.appendChild(prevLi);

        // Page numbers
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        if (endPage - startPage < 4 && totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement("li");
            pageLi.className = `page-item ${i === currentPage ? "active" : ""}`;
            pageLi.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            pagination.appendChild(pageLi);
        }

        // Next button
        const nextLi = document.createElement("li");
        nextLi.className = `page-item ${
            currentPage === totalPages ? "disabled" : ""
        }`;
        nextLi.innerHTML = `<a class="page-link" href="#" data-page="${
            currentPage + 1
        }">&raquo;</a>`;
        pagination.appendChild(nextLi);

        // Add event listeners to pagination links
        document.querySelectorAll(".page-link").forEach((link) => {
            link.addEventListener("click", function (e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute("data-page"));
                if (page >= 1 && page <= totalPages) {
                    currentPage = page;
                    loadInvoices();
                }
            });
        });
    }

    // Function to update showing text
    function updateShowingText(data) {
        const showingText = document.getElementById("showing-text");
        const from =
            data.total > 0 ? (data.current_page - 1) * data.per_page + 1 : 0;
        const to = Math.min(data.total, data.current_page * data.per_page);
        showingText.textContent = `Menampilkan ${from} - ${to} dari ${data.total} invoice`;
    }

    // Function to filter invoices client-side
    function filterAndDisplayInvoices() {
        if (searchTerm === "") {
            // If search term is empty, use all loaded invoices
            filteredInvoices = [...allLoadedInvoices];
        } else {
            // Filter invoices based on search term
            filteredInvoices = allLoadedInvoices.filter((invoice) => {
                // Search in multiple fields
                // Check if invoice is past due
                const today = new Date();
                const dueDate = invoice.due_date
                    ? new Date(invoice.due_date)
                    : null;
                const isPastDue =
                    dueDate &&
                    dueDate < today &&
                    invoice.payment_status !== "Lunas";
                const dueStatusText = isPastDue ? "jatuh tempo" : "belum tempo";

                // Check if this is a manual invoice
                const isManualInvoice =
                    invoice.unit === "Manual" || invoice.unit_list === "Manual";
                const isSearchingForManual = searchTerm === "manual";

                // Calculate termin for search
                let termin = 60; // Default
                if (invoice.tanggal_invoice && invoice.due_date) {
                    const invoiceDate = new Date(invoice.tanggal_invoice);
                    const dueDate = new Date(invoice.due_date);
                    const diffTime = Math.abs(dueDate - invoiceDate);
                    termin = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                }
                const terminText = termin + " hari";

                // Format currency values for search
                const formattedSubtotal = new Intl.NumberFormat("id-ID", {
                    style: "currency",
                    currency: "IDR",
                    minimumFractionDigits: 0,
                })
                    .format(invoice.subtotal || 0)
                    .toLowerCase();

                const formattedTax = new Intl.NumberFormat("id-ID", {
                    style: "currency",
                    currency: "IDR",
                    minimumFractionDigits: 0,
                })
                    .format(invoice.tax_amount || 0)
                    .toLowerCase();

                const formattedTotal = new Intl.NumberFormat("id-ID", {
                    style: "currency",
                    currency: "IDR",
                    minimumFractionDigits: 0,
                })
                    .format(invoice.total_amount || 0)
                    .toLowerCase();

                // Get PO number using the helper function
                const poNumber = getPONumberFromInvoice(invoice);

                return (
                    (invoice.no_invoice &&
                        invoice.no_invoice
                            .toLowerCase()
                            .includes(searchTerm)) ||
                    (invoice.customer &&
                        invoice.customer.toLowerCase().includes(searchTerm)) ||
                    (invoice.unit_list &&
                        invoice.unit_list.toLowerCase().includes(searchTerm)) ||
                    (poNumber && poNumber.toLowerCase().includes(searchTerm)) || // Search by PO number from helper function
                    (invoice.site_name &&
                        invoice.site_name.toLowerCase().includes(searchTerm)) ||
                    (invoice.payment_status &&
                        invoice.payment_status
                            .toLowerCase()
                            .includes(searchTerm)) ||
                    (invoice.trouble &&
                        invoice.trouble.toLowerCase().includes(searchTerm)) || // Search by kategori
                    (invoice.sn &&
                        invoice.sn.toLowerCase().includes(searchTerm)) || // Search by SN
                    (invoice.notes &&
                        invoice.notes.toLowerCase().includes(searchTerm)) || // Search by keterangan
                    formattedSubtotal.includes(searchTerm) || // Search by nilai invoice
                    formattedTax.includes(searchTerm) || // Search by tax
                    formattedTotal.includes(searchTerm) || // Search by total
                    terminText.includes(searchTerm) || // Search by termin
                    dueStatusText.includes(searchTerm) ||
                    (isManualInvoice && isSearchingForManual) // Allow searching for "manual" invoices
                );
            });
        }

        // Apply client-side pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedInvoices = filteredInvoices.slice(startIndex, endIndex);
        // Display the filtered and paginated invoices
        displayInvoices(paginatedInvoices);

        // Update pagination and showing text
        updateClientPagination();
        updateClientShowingText();
    }

    // Function to sort invoices client-side
    function sortInvoicesClientSide() {
        filteredInvoices.sort((a, b) => {
            let valueA, valueB;

            // Handle different field types
            switch (sortField) {
                case "no_invoice":
                    valueA = a.no_invoice || "";
                    valueB = b.no_invoice || "";
                    break;
                case "customer":
                    valueA = a.customer || "";
                    valueB = b.customer || "";
                    break;
                case "trouble":
                    valueA = a.trouble || "";
                    valueB = b.trouble || "";
                    break;
                case "nomor_po":
                    valueA = getPONumberFromInvoice(a);
                    valueB = getPONumberFromInvoice(b);
                    break;
                case "sn":
                    valueA = a.sn || "";
                    valueB = b.sn || "";
                    break;
                case "notes":
                    valueA = a.notes || "";
                    valueB = b.notes || "";
                    break;
                case "tanggal_invoice":
                    valueA = a.tanggal_invoice
                        ? new Date(a.tanggal_invoice)
                        : new Date(0);
                    valueB = b.tanggal_invoice
                        ? new Date(b.tanggal_invoice)
                        : new Date(0);
                    break;
                case "termin":
                    // Calculate termin for invoice A
                    let terminA = 60; // Default
                    if (a.tanggal_invoice && a.due_date) {
                        const invoiceDateA = new Date(a.tanggal_invoice);
                        const dueDateA = new Date(a.due_date);
                        const diffTimeA = Math.abs(dueDateA - invoiceDateA);
                        terminA = Math.ceil(diffTimeA / (1000 * 60 * 60 * 24));
                    }

                    // Calculate termin for invoice B
                    let terminB = 60; // Default
                    if (b.tanggal_invoice && b.due_date) {
                        const invoiceDateB = new Date(b.tanggal_invoice);
                        const dueDateB = new Date(b.due_date);
                        const diffTimeB = Math.abs(dueDateB - invoiceDateB);
                        terminB = Math.ceil(diffTimeB / (1000 * 60 * 60 * 24));
                    }

                    valueA = terminA;
                    valueB = terminB;
                    break;
                case "due_date":
                    valueA = a.due_date ? new Date(a.due_date) : new Date(0);
                    valueB = b.due_date ? new Date(b.due_date) : new Date(0);
                    break;
                case "subtotal":
                    valueA = parseFloat(a.subtotal || 0);
                    valueB = parseFloat(b.subtotal || 0);
                    break;
                case "tax_amount":
                    valueA = parseFloat(a.tax_amount || 0);
                    valueB = parseFloat(b.tax_amount || 0);
                    break;
                case "total_amount":
                    valueA = parseFloat(a.total_amount || 0);
                    valueB = parseFloat(b.total_amount || 0);
                    break;
                case "payment_status":
                    valueA = a.payment_status || "";
                    valueB = b.payment_status || "";
                    break;
                default:
                    valueA = a[sortField] || "";
                    valueB = b[sortField] || "";
            }

            // Compare values based on sort direction
            if (sortDirection === "asc") {
                if (valueA < valueB) return -1;
                if (valueA > valueB) return 1;
                return 0;
            } else {
                if (valueA > valueB) return -1;
                if (valueA < valueB) return 1;
                return 0;
            }
        });
    }

    // Function to update pagination for client-side filtering
    function updateClientPagination() {
        const pagination = document.getElementById("pagination");
        pagination.innerHTML = "";

        // Calculate total pages based on filtered data
        totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);

        // Ensure current page is valid
        if (currentPage > totalPages) {
            currentPage = Math.max(1, totalPages);
        }

        // Previous button
        const prevLi = document.createElement("li");
        prevLi.className = `page-item ${currentPage === 1 ? "disabled" : ""}`;
        prevLi.innerHTML = `<a class="page-link" href="#" data-page="${
            currentPage - 1
        }">&laquo;</a>`;
        pagination.appendChild(prevLi);

        // Page numbers
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        if (endPage - startPage < 4 && totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement("li");
            pageLi.className = `page-item ${i === currentPage ? "active" : ""}`;
            pageLi.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            pagination.appendChild(pageLi);
        }

        // Next button
        const nextLi = document.createElement("li");
        nextLi.className = `page-item ${
            currentPage === totalPages ? "disabled" : ""
        }`;
        nextLi.innerHTML = `<a class="page-link" href="#" data-page="${
            currentPage + 1
        }">&raquo;</a>`;
        pagination.appendChild(nextLi);

        // Add event listeners to pagination links
        document.querySelectorAll(".page-link").forEach((link) => {
            link.addEventListener("click", function (e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute("data-page"));
                if (page >= 1 && page <= totalPages) {
                    currentPage = page;
                    filterAndDisplayInvoices();
                }
            });
        });
    }

    // Function to update showing text for client-side filtering
    function updateClientShowingText() {
        const showingText = document.getElementById("showing-text");
        const total = filteredInvoices.length;
        const from = total > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
        const to = Math.min(total, currentPage * itemsPerPage);
        showingText.textContent = `Menampilkan ${from} - ${to} dari ${total} invoice`;
    }

    // Function to update invoice status
    // Function to update invoice status
    function updateInvoiceStatus(invoiceId, newStatus) {
        // If changing to Lunas, show payment date modal
        if (newStatus === "Lunas") {
            // First check if this invoice already has a document
            const invoice = allLoadedInvoices.find(
                (inv) => inv.id == invoiceId
            );

            if (!invoice || !invoice.document_path) {
                // Show confirmation with warning about document requirement
                Swal.fire({
                    title: "Perhatian!",
                    text: "Untuk mengubah status menjadi Lunas, dokumen invoice asli harus dilampirkan. Apakah Anda ingin melanjutkan?",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonText: "Ya, Lanjutkan",
                    cancelButtonText: "Batal",
                }).then((result) => {
                    if (result.isConfirmed) {
                        showPaymentDateModal(invoiceId);
                    }
                });
                return;
            } else {
                showPaymentDateModal(invoiceId);
                return;
            }
        }

        // If not changing to Lunas, proceed directly
        processStatusUpdate(invoiceId, newStatus);
    }

    // Function to show payment date modal
    function showPaymentDateModal(invoiceId) {
        const modal = new bootstrap.Modal(
            document.getElementById("paymentDateModal")
        );

        // Set default date to today
        const today = new Date().toISOString().split("T")[0];
        document.getElementById("paymentDateInput").value = today;

        // Clear previous event listeners
        document
            .getElementById("confirmPaymentDate")
            .replaceWith(
                document.getElementById("confirmPaymentDate").cloneNode(true)
            );

        // Add new event listener
        document
            .getElementById("confirmPaymentDate")
            .addEventListener("click", function () {
                const paymentDate =
                    document.getElementById("paymentDateInput").value;
                if (!paymentDate) {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "Silakan pilih tanggal pelunasan",
                        confirmButtonText: "OK",
                    });
                    return;
                }

                modal.hide();
                processStatusUpdate(invoiceId, "Lunas", paymentDate);
            });

        modal.show();
    }

    // Function to process the status update (modified to accept paymentDate)
    function processStatusUpdate(invoiceId, newStatus, paymentDate = null) {
        // Show loading indicator
        Swal.fire({
            title: "Updating...",
            text: "Updating invoice status",
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            },
        });

        // Prepare form data
        const formData = new FormData();
        formData.append("invoice_id", invoiceId);
        formData.append("payment_status", newStatus);

        if (newStatus === "Lunas") {
            // If status is being set to Lunas, set payment date
            formData.append(
                "payment_date",
                paymentDate || new Date().toISOString().split("T")[0]
            );
        }

        // Send request to update status
        fetch("/sales/invoices/payment-status", {
            method: "POST",
            headers: {
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
            body: formData,
        })
            .then((response) => response.json())
            .then((data) => {
                Swal.close();

                if (data.success) {
                    // Show success message
                    Swal.fire({
                        icon: "success",
                        title: "Berhasil",
                        text: "Status invoice berhasil diperbarui",
                        timer: 1500,
                        showConfirmButton: false,
                    }).then(() => {
                        // Reload invoices
                        loadInvoices();
                    });
                } else {
                    // Show error message
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text:
                            data.message || "Gagal memperbarui status invoice",
                        confirmButtonText: "OK",
                    });
                }
            })
            .catch((error) => {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Terjadi kesalahan saat memperbarui status invoice",
                    confirmButtonText: "OK",
                });
            });
        document.querySelectorAll(".modal-backdrop").forEach(function (el) {
            el.style.display = "none";
        });
    }

    // Function to close document modal
    function closeDocumentModal() {
        const modalElement = document.getElementById("document-viewer-modal");

        // Hide modal
        modalElement.classList.remove("show");
        modalElement.style.display = "none";
        document.body.classList.remove("modal-open");

        // Remove backdrop
        const backdrop = document.querySelector(".modal-backdrop");
        if (backdrop) {
            backdrop.remove();
        }
    }

    // Function to show document in modal
    function showDocumentInModal(documentPath) {
        const modalElement = document.getElementById("document-viewer-modal");
        const documentViewer = document.getElementById("document-viewer");
        const downloadLink = document.getElementById("download-document");
        const modalTitle = document.getElementById(
            "document-viewer-modal-label"
        );
        const closeButtons = modalElement.querySelectorAll(
            '[data-bs-dismiss="modal"], .close, button.btn-secondary'
        );

        // Update modal title with filename
        const fileName = documentPath.split("/").pop();
        modalTitle.textContent = `Dokumen Invoice: ${fileName}`;

        // Set the document path for download button
        downloadLink.href = `/assets/invoice_documents/${documentPath}`;

        // Show loading state
        documentViewer.innerHTML = `
            <div class="d-flex justify-content-center align-items-center h-100">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">.</span>
                </div>
            </div>
        `;

        // Add specific event listeners for this modal's close buttons
        closeButtons.forEach((button) => {
            // Remove existing listeners to avoid duplicates
            button.removeEventListener("click", closeDocumentModal);
            // Add new listener
            button.addEventListener("click", closeDocumentModal);
        });

        // Show modal manually
        modalElement.classList.add("show");
        modalElement.style.display = "block";
        document.body.classList.add("modal-open");

        // Add backdrop if it doesn't exist
        if (!document.querySelector(".modal-backdrop")) {
            const backdrop = document.createElement("div");
            backdrop.className = "modal-backdrop fade show";
            // Add click event to backdrop to close modal when clicking outside
            backdrop.addEventListener("click", closeDocumentModal);
            document.body.appendChild(backdrop);
        }

        // Check file extension to determine how to display it
        const fileExtension = documentPath.split(".").pop().toLowerCase();

        if (fileExtension === "pdf") {
            // Embed PDF viewer with 100% width and height
            documentViewer.innerHTML = `
                <iframe src="/assets/invoice_documents/${documentPath}" style="width: 100%; height: 100%; border: none;"></iframe>
            `;
        } else if (["jpg", "jpeg", "png", "gif"].includes(fileExtension)) {
            // Display image
            documentViewer.innerHTML = `
                <div class="d-flex justify-content-center align-items-center h-100">
                    <img src="/assets/invoice_documents/${documentPath}" class="img-fluid" style="max-height: 100%; max-width: 100%;">
                </div>
            `;
        } else {
            // For other file types, show a message and download link
            documentViewer.innerHTML = `
                <div class="d-flex flex-column justify-content-center align-items-center h-100">
                    <div class="alert alert-info">
                        <i class="mdi mdi-file-document-outline" style="font-size: 48px;"></i>
                        <p class="mt-3">File tidak dapat ditampilkan secara langsung. Silakan download file untuk melihatnya.</p>
                    </div>
                </div>
            `;
        }
    }

    // Function to load sites for dropdown
    function loadSites() {
        const siteSelect = document.getElementById("site_id");

        // Clear existing options except the first one
        while (siteSelect.options.length > 1) {
            siteSelect.remove(1);
        }

        // Show loading state
        const loadingOption = document.createElement("option");
        loadingOption.textContent = ".";
        loadingOption.disabled = true;
        siteSelect.appendChild(loadingOption);
        fetch("/api/sites")
            .then((response) => response.json())
            .then((data) => {
                siteSelect.remove(siteSelect.options.length - 1);
                data.forEach((site) => {
                    if (site.name !== "Warehouse") {
                        const option = document.createElement("option");
                        option.value = site.id;
                        option.textContent = site.name;
                        siteSelect.appendChild(option);
                    }
                });
                const nonSiteOption = document.createElement("option");
                nonSiteOption.value = "";
                nonSiteOption.textContent = "Non-Site";
                siteSelect.appendChild(nonSiteOption);
            })
            .catch((error) => {
                siteSelect.innerHTML =
                    '<option value="">Error loading sites</option>';
            });
    }

    // Function to edit an invoice
    function editInvoice(invoiceId) {
        Swal.fire({
            title: ".",
            text: "Memuat data invoice",
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            },
        });

        // Fetch invoice details
        fetch(`/sales/invoices/${invoiceId}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .catch((error) => {
                Swal.fire({
                    icon: "error",
                    title: "Network Error",
                    text: "Terjadi kesalahan jaringan saat memuat data invoice. Silakan coba lagi.",
                    confirmButtonText: "OK",
                });
                throw error; // Re-throw to prevent further processing
            })
            .then((data) => {
                Swal.close();

                if (data.success) {
                    const invoice = data.invoice;

                    // Change modal title
                    document.getElementById(
                        "direct-invoice-modal-label"
                    ).textContent = "Edit Invoice";

                    // Helper function to safely set element value
                    function setElementValue(id, value) {
                        const element = document.getElementById(id);
                        if (element) {
                            element.value = value;
                        }
                    }

                    // Format dates for input fields (YYYY-MM-DD) - timezone safe
                    function formatDateForInput(dateString) {
                        if (!dateString) return "";

                        // Use global DateUtils if available for timezone-safe parsing
                        if (
                            window.DateUtils &&
                            window.DateUtils.formatDateForInput
                        ) {
                            return window.DateUtils.formatDateForInput(
                                dateString
                            );
                        }

                        // If it's already in YYYY-MM-DD format, return it
                        if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                            return dateString;
                        }

                        // Handle Laravel date format (which might include time)
                        if (typeof dateString === "object" && dateString.date) {
                            dateString = dateString.date;
                        }

                        // Timezone-safe date parsing
                        try {
                            let cleanDateString = dateString;

                            // Extract date part if it contains time
                            if (dateString.includes("T")) {
                                // ISO format: YYYY-MM-DDTHH:MM:SS.000000Z
                                cleanDateString = dateString.split("T")[0];
                            } else if (dateString.includes(" ")) {
                                // MySQL format: YYYY-MM-DD HH:MM:SS
                                cleanDateString = dateString.split(" ")[0];
                            }

                            // If it's in YYYY-MM-DD format after cleaning, return it
                            if (/^\d{4}-\d{2}-\d{2}$/.test(cleanDateString)) {
                                return cleanDateString;
                            }

                            // Handle DD/MM/YYYY format
                            if (cleanDateString.includes("/")) {
                                const parts = cleanDateString.split("/");
                                if (parts.length === 3) {
                                    // Assuming DD/MM/YYYY format
                                    const day = parts[0].padStart(2, "0");
                                    const month = parts[1].padStart(2, "0");
                                    const year = parts[2];
                                    return `${year}-${month}-${day}`;
                                }
                            }
                            return "";
                        } catch (e) {
                            return "";
                        }
                    }

                    // Set form values
                    setElementValue("invoice_id", invoice.id);
                    setElementValue(
                        "tanggal_invoice",
                        formatDateForInput(invoice.tanggal_invoice)
                    );
                    setElementValue(
                        "due_date",
                        formatDateForInput(invoice.due_date)
                    );
                    setElementValue("no_invoice", invoice.no_invoice || "");
                    setElementValue("customeredit", invoice.customer || "");
                    setElementValue("trouble", invoice.trouble || "");
                    setElementValue("po_number", invoice.po_number || "");
                    setElementValue("sn", invoice.sn || "");
                    setElementValue("site_id", invoice.site_id || "");
                    setElementValue("unit", "Manual"); // Always set to Manual for edit
                    setElementValue("lokasi", invoice.lokasi || "");
                    setElementValue("model_unit", invoice.model_unit || "");
                    setElementValue("hmkm", invoice.hmkm || "");
                    setElementValue("notes", invoice.notes || "");

                    // For direct invoices, use direct_subtotal
                    const subtotalValue = invoice.direct_subtotal || "0";
                    setElementValue("subtotal", subtotalValue);

                    // Convert ppn from decimal to percentage (e.g., 0.11 to 11)
                    let ppnValue = 11; // Default value
                    if (invoice.ppn !== null && invoice.ppn !== undefined) {
                        if (invoice.ppn <= 1) {
                            // If decimal, convert to percent
                            ppnValue = Math.round(invoice.ppn * 100);
                        } else {
                            // Already percent
                            ppnValue = invoice.ppn;
                        }
                    }
                    setElementValue("ppn", ppnValue);

                    // Show the modal
                    const modal = document.getElementById(
                        "direct-invoice-modal"
                    );
                    modal.classList.add("show");
                    modal.style.display = "block";
                    document.body.classList.add("modal-open");

                    // Add backdrop if it doesn't exist
                    if (!document.querySelector(".modal-backdrop")) {
                        const backdrop = document.createElement("div");
                        backdrop.className = "modal-backdrop fade show";
                        document.body.appendChild(backdrop);
                    }
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: data.message || "Gagal memuat data invoice",
                        confirmButtonText: "OK",
                    });
                }
            })
            .catch((error) => {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text:
                        "Terjadi kesalahan saat memuat data invoice: " +
                        error.message,
                    confirmButtonText: "OK",
                });
            });
    }

    // Save Direct Invoice button click event
    document
        .getElementById("save-direct-invoice-btn")
        .addEventListener("click", function () {
            // Get form data
            const form = document.getElementById("direct-invoice-form");
            const formData = new FormData(form);

            // Check if this is an update or create operation
            const isUpdate = document.getElementById("invoice_id").value !== "";

            // Only validate invoice number
            const noInvoice = document
                .getElementById("no_invoice")
                .value.trim();
            if (!noInvoice) {
                Swal.fire({
                    icon: "error",
                    title: "Data Tidak Lengkap",
                    html: "Mohon lengkapi kolom <strong>No Invoice</strong>",
                    confirmButtonText: "OK",
                });
                return;
            }

            // Check if document is attached
            const documentElement = document.getElementById("document");
            if (documentElement && !documentElement.files.length) {
                Swal.fire({
                    icon: "warning",
                    title: "Dokumen Lampiran",
                    text: "Anda belum melampirkan dokumen. Lanjutkan tanpa lampiran?",
                    showCancelButton: true,
                    confirmButtonText: "Ya, Lanjutkan",
                    cancelButtonText: "Batal",
                }).then((result) => {
                    if (result.isConfirmed) {
                        submitForm(formData);
                    }
                });
            } else {
                submitForm(formData);
            }
        });

    // Function to submit the form data
    function submitForm(formData) {
        // Add CSRF token
        const csrfToken = document
            .querySelector('meta[name="csrf-token"]')
            .getAttribute("content");

        // Check if this is an update or create operation
        const isUpdate = document.getElementById("invoice_id").value !== "";

        // Set the appropriate URL and method
        const url = isUpdate
            ? `/sales/invoices/${document.getElementById("invoice_id").value}`
            : "/sales/direct-invoices";
        const method = isUpdate ? "PUT" : "POST";

        // Show loading state
        Swal.fire({
            title: isUpdate ? "Memperbarui..." : "Menyimpan...",
            text: isUpdate
                ? "Sedang memperbarui invoice"
                : "Sedang menyimpan invoice",
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            },
        });

        // If this is an update, add the _method field for Laravel method spoofing
        if (isUpdate) {
            formData.append("_method", "PUT");
        }

        // Send request to create or update invoice
        fetch(url, {
            method: "POST", // Always use POST for FormData (method spoofing for PUT)
            headers: {
                "X-CSRF-TOKEN": csrfToken,
            },
            body: formData,
        })
            .then((response) => response.json())
            .then((data) => {
                Swal.close();

                if (data.success) {
                    // Show success message
                    Swal.fire({
                        icon: "success",
                        title: "Berhasil",
                        text:
                            data.message ||
                            (isUpdate
                                ? "Invoice berhasil diperbarui"
                                : "Invoice manual berhasil dibuat"),
                        timer: 1500,
                        showConfirmButton: false,
                    }).then(() => {
                        // Close modal
                        const modal = document.getElementById(
                            "direct-invoice-modal"
                        );
                        modal.classList.remove("show");
                        modal.style.display = "none";
                        document.body.classList.remove("modal-open");

                        // Remove backdrop
                        const backdrop =
                            document.querySelector(".modal-backdrop");
                        if (backdrop) {
                            backdrop.remove();
                        }

                        // Reset form and modal title
                        document.getElementById("direct-invoice-form").reset();
                        document.getElementById("invoice_id").value = "";
                        document.getElementById(
                            "direct-invoice-modal-label"
                        ).textContent = "Tambah Invoice Manual";

                        // Reload invoices
                        loadInvoices();
                    });
                } else {
                    // Show error message
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text:
                            data.message ||
                            (isUpdate
                                ? "Gagal memperbarui invoice"
                                : "Gagal membuat invoice manual"),
                        confirmButtonText: "OK",
                    });
                }
            })
            .catch((error) => {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: isUpdate
                        ? "Terjadi kesalahan saat memperbarui invoice"
                        : "Terjadi kesalahan saat membuat invoice manual",
                    confirmButtonText: "OK",
                });
            });
    }

    // Function to delete an invoice
    function deleteInvoice(invoiceId) {
        // Show confirmation dialog
        Swal.fire({
            title: "Konfirmasi Hapus",
            text: "Apakah Anda yakin ingin menghapus invoice ini?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Ya, Hapus",
            cancelButtonText: "Batal",
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: "Menghapus...",
                    text: "Sedang menghapus invoice",
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    },
                });

                // Send delete request
                fetch(`/sales/invoices/${invoiceId}`, {
                    method: "DELETE",
                    headers: {
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                        "Content-Type": "application/json",
                    },
                })
                    .then((response) => response.json())
                    .then((data) => {
                        Swal.close();

                        if (data.success) {
                            // Show success message
                            Swal.fire({
                                icon: "success",
                                title: "Berhasil",
                                text: "Invoice berhasil dihapus",
                                timer: 1500,
                                showConfirmButton: false,
                            }).then(() => {
                                // Reload invoices
                                loadInvoices();
                            });
                        } else {
                            // Show error message
                            Swal.fire({
                                icon: "error",
                                title: "Error",
                                text: data.message || "Gagal menghapus invoice",
                                confirmButtonText: "OK",
                            });
                        }
                    })
                    .catch((error) => {
                        console.error("Error deleting invoice:", error);
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "Terjadi kesalahan saat menghapus invoice",
                            confirmButtonText: "OK",
                        });
                    });
            }
        });
    }
});
