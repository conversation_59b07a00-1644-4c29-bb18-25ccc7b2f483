<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Part;
use App\Models\ManualInvoicePart;
use App\Models\CustomerSales;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogHelper;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class ManualInvoiceController extends Controller
{
    /**
     * Show the form for creating a new invoice
     */
    public function create()
    {
        return view('sales.invoices.create');
    }


    /**
     * Store a new manual invoice without unit requirement
     */
    public function storeManualInvoice(Request $request)
    {
        try {
            // Validate the request - removed unit requirement
            $request->validate([
                'customer' => 'nullable|string|max:255',
                'customer_code' => 'nullable|string|exists:customersales,code',
                'site_id' => 'nullable|exists:sites,id',
                'no_invoice' => 'required|string|max:255',
                'po_number' => 'nullable|string|max:255',
                'sn' => 'nullable|string|max:255',
                'trouble' => 'nullable|string',
                'lokasi' => 'nullable|string|max:255',
                'location' => 'nullable|string|max:255',
                'model_unit' => 'nullable|string|max:255',
                'hmkm' => 'nullable|string|max:255',
                'tanggal_invoice' => 'required|date',
                'due_date' => 'nullable|date|after_or_equal:tanggal_invoice',
                'direct_subtotal' => 'required|numeric|min:0',
                'ppn' => 'nullable|numeric|min:0|max:100',
                'transfer_to' => 'nullable|string|max:255',
                'bank_account' => 'nullable|string|max:255',
                'bank_branch' => 'nullable|string|max:255',
                'notes' => 'nullable|string',
                'document' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx',
                'payment_status' => 'nullable|string|in:Lunas,Belum Lunas,Draf',
                'parts' => 'nullable|array',
                'parts.*.part_code' => 'required_with:parts|string|exists:parts,part_code',
                'parts.*.part_name' => 'required_with:parts|string',
                'parts.*.quantity' => 'required_with:parts|integer|min:1',
                'parts.*.price' => 'required_with:parts|numeric|min:0',
                'parts.*.eum' => 'nullable|string|max:5'
            ], [
                'no_invoice.required' => 'Nomor invoice harus diisi',
                'tanggal_invoice.required' => 'Tanggal invoice harus diisi',
                'direct_subtotal.required' => 'Nilai invoice harus diisi',
                'direct_subtotal.min' => 'Nilai invoice tidak boleh kurang dari 0',
                'document.max' => 'Ukuran file tidak boleh lebih dari 10MB',
                'document.mimes' => 'Format file harus PDF, JPG, JPEG, PNG, DOC, atau DOCX'
            ]);

            // Start a database transaction
            DB::beginTransaction();

            try {
                // Generate invoice number if not provided
                $invoiceNumber = $request->no_invoice;
                if (empty($invoiceNumber)) {
                    $invoiceNumber = $this->generateNextInvoiceNumber();
                }

                // Convert PPN from percentage to decimal (e.g., 11% to 0.11)
                // Make sure ppn is between 0 and 100
                $ppn = $request->ppn ?? 11;
                $ppn = max(0, min(100, $ppn)); // Clamp between 0 and 100
                $ppnDecimal = $ppn / 100;

                // Handle file upload if present
                $documentPath = null;
                if ($request->hasFile('document')) {
                    $file = $request->file('document');
                    $fileName = time() . '_' . $file->getClientOriginalName();
                    $file->move(public_path('assets/invoice_documents'), $fileName);
                    $documentPath = $fileName;
                }

                // Set due date if not provided (60 days from invoice date)
                $dueDate = $request->due_date;
                if (empty($dueDate) && !empty($request->tanggal_invoice)) {
                    $invoiceDate = Carbon::parse($request->tanggal_invoice);
                    $dueDate = $invoiceDate->copy()->addDays(60)->format('Y-m-d');
                }

                // Create new invoice
                $invoice = Invoice::create([
                    'customer' => $request->customer,
                    'customer_code' => $request->customer_code,
                    'site_id' => $request->site_id,
                    'location' => $request->location,
                    'no_invoice' => $invoiceNumber,
                    'po_number' => $request->po_number,
                    'sn' => $request->sn,
                    'trouble' => $request->trouble ?: 'Manual Invoice', // Default to "Manual Invoice" if empty
                    'lokasi' => $request->lokasi,
                    'model_unit' => $request->model_unit,
                    'hmkm' => $request->hmkm,
                    'tanggal_invoice' => $request->tanggal_invoice,
                    'due_date' => $dueDate,
                    'ppn' => $ppnDecimal,
                    'payment_status' => $request->payment_status ?? 'Belum Lunas',
                    'transfer_to' => $request->transfer_to,
                    'bank_account' => $request->bank_account,
                    'bank_branch' => $request->bank_branch,
                    'notes' => $request->notes,
                    'document_path' => $documentPath,
                    'direct_subtotal' => $request->direct_subtotal
                ]);

                // Save parts if provided
                if ($request->has('parts') && is_array($request->parts)) {
                    foreach ($request->parts as $partData) {
                        $total = $partData['quantity'] * $partData['price'];

                        ManualInvoicePart::create([
                            'invoice_id' => $invoice->id,
                            'part_code' => $partData['part_code'],
                            'part_name' => $partData['part_name'],
                            'quantity' => $partData['quantity'],
                            'price' => $partData['price'],
                            'total' => $total,
                            'eum' => $partData['eum'] ?? 'EA'
                        ]);
                    }
                }

                // Commit the transaction
                DB::commit();

                $message = 'Invoice manual berhasil dibuat';
                $logAction = 'Create Manual Invoice';
                $logDescription = 'Invoice manual baru dibuat dengan nomor ' . $invoiceNumber;

                // Log the action
                LogHelper::createLog(
                    $logAction,
                    $logDescription,
                    'Invoice',
                    $request
                );

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'invoice' => $invoice
                ]);
            } catch (\Exception $innerException) {
                // Roll back the transaction
                DB::rollBack();
                throw $innerException;
            }
        } catch (\Exception $exception) {
            \Log::error($exception->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat invoice manual: ' . $exception->getMessage()
            ], 422);
        }
    }

    /**
     * Generate the next invoice number based on the last invoice
     */
    private function generateNextInvoiceNumber()
    {
        // Get the current month and year
        $month = Carbon::now()->format('m');
        $year = Carbon::now()->format('Y');

        // Find the latest invoice
        $latestInvoice = Invoice::orderBy('created_at', 'desc')->first();

        if ($latestInvoice) {
            // Extract the numeric part before the first slash
            $parts = explode('/', $latestInvoice->no_invoice);
            $numericPart = (int) $parts[0];

            // Increment the numeric part
            $nextNumericPart = $numericPart + 1;

            // Pad the numeric part to the same length as the original
            $originalLength = strlen($parts[0]);
            $paddedNumericPart = str_pad($nextNumericPart, $originalLength, '0', STR_PAD_LEFT);

            // Return the new invoice number with current month/year
            return sprintf('%s/INV-PWB/%s/%s', $paddedNumericPart, $month, $year);
        } else {
            // Start with 001 if no invoice exists (default format)
            return sprintf('%03d/INV-PWB/%s/%s', 1, $month, $year);
        }
    }

    /**
     * Get the latest invoice for auto-increment
     */
    public function getLatestInvoice()
    {
        try {
            $latestInvoice = Invoice::orderBy('created_at', 'desc')->first();

            return response()->json([
                'success' => true,
                'latest_invoice' => $latestInvoice
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil invoice terbaru: ' . $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Search parts for autocomplete functionality
     */
    public function searchParts(Request $request)
    {
        try {
            $query = $request->get('query', '');
            $limit = $request->get('limit', 10);

            if (strlen($query) < 2) {
                return response()->json([
                    'success' => true,
                    'parts' => []
                ]);
            }

            $parts = Part::where(function($q) use ($query) {
                $q->where('part_code', 'LIKE', "%{$query}%")
                  ->orWhere('part_name', 'LIKE', "%{$query}%");
            })
            ->select('part_code', 'part_name', 'price', 'part_type', 'eum')
            ->limit($limit)
            ->get();

            return response()->json([
                'success' => true,
                'parts' => $parts
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mencari parts: ' . $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Search customers for autocomplete functionality
     */
    public function searchCustomers(Request $request)
    {
        try {
            $query = $request->get('query', '');
            $limit = $request->get('limit', 10);

            if (strlen($query) < 2) {
                return response()->json([
                    'success' => true,
                    'customers' => []
                ]);
            }

            $customers = CustomerSales::where(function($q) use ($query) {
                $q->where('code', 'LIKE', "%{$query}%")
                  ->orWhere('nama_customer', 'LIKE', "%{$query}%");
            })
            ->select('code', 'nama_customer', 'alamat')
            ->limit($limit)
            ->get();

            return response()->json([
                'success' => true,
                'customers' => $customers
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mencari customer: ' . $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Update manual invoice
     */
    public function updateManualInvoice(Request $request, $id)
    {
        try {
            // Validate the request
            $request->validate([
                'customer' => 'nullable|string|max:255',
                'customer_code' => 'nullable|string|exists:customersales,code',
                'site_id' => 'nullable|exists:sites,id',
                'no_invoice' => 'required|string|max:255',
                'po_number' => 'nullable|string|max:255',
                'sn' => 'nullable|string|max:255',
                'trouble' => 'nullable|string',
                'lokasi' => 'nullable|string|max:255',
                'model_unit' => 'nullable|string|max:255',
                'hmkm' => 'nullable|string|max:255',
                'tanggal_invoice' => 'required|date',
                'due_date' => 'nullable|date|after_or_equal:tanggal_invoice',
                'direct_subtotal' => 'required|numeric|min:0',
                'ppn' => 'nullable|numeric|min:0|max:100',
                'payment_status' => 'nullable|in:Lunas,Belum Lunas,Jatuh Tempo,Pending',
                'notes' => 'nullable|string',
                'parts' => 'nullable|array',
                'parts.*.part_code' => 'required_with:parts|string|max:255',
                'parts.*.part_name' => 'required_with:parts|string|max:255',
                'parts.*.quantity' => 'required_with:parts|numeric|min:1',
                'parts.*.price' => 'required_with:parts|numeric|min:0',
                'parts.*.eum' => 'required_with:parts|string|max:10',
            ]);

            DB::beginTransaction();

            try {
                // Find the invoice
                $invoice = Invoice::whereNotNull('direct_subtotal')->findOrFail($id);

                // Calculate due date if not provided
                $dueDate = $request->due_date;
                if (empty($dueDate)) {
                    $invoiceDate = Carbon::parse($request->tanggal_invoice);
                    $dueDate = $invoiceDate->copy()->addDays(60)->format('Y-m-d');
                }

                // Convert PPN from percentage to decimal
                $ppn = $request->ppn ?? 11;
                $ppn = max(0, min(100, $ppn));
                $ppnDecimal = $ppn / 100;

                // Update invoice
                $invoice->update([
                    'customer' => $request->customer,
                    'customer_code' => $request->customer_code,
                    'site_id' => $request->site_id,
                    'location' => $request->location,
                    'no_invoice' => $request->no_invoice,
                    'po_number' => $request->po_number,
                    'sn' => $request->sn,
                    'trouble' => $request->trouble ?: 'Manual Invoice',
                    'lokasi' => $request->lokasi,
                    'model_unit' => $request->model_unit,
                    'hmkm' => $request->hmkm,
                    'tanggal_invoice' => $request->tanggal_invoice,
                    'due_date' => $dueDate,
                    'ppn' => $ppnDecimal,
                    'payment_status' => $request->payment_status ?? 'Belum Lunas',
                    'transfer_to' => $request->transfer_to,
                    'bank_account' => $request->bank_account,
                    'bank_branch' => $request->bank_branch,
                    'notes' => $request->notes,
                    'direct_subtotal' => $request->direct_subtotal,
                ]);

                // Delete existing parts
                ManualInvoicePart::where('invoice_id', $invoice->id)->delete();

                // Add new parts if provided
                if ($request->has('parts') && is_array($request->parts)) {
                    foreach ($request->parts as $partData) {
                        if (!empty($partData['part_code']) && !empty($partData['part_name'])) {
                            $total = $partData['quantity'] * $partData['price'];

                            ManualInvoicePart::create([
                                'invoice_id' => $invoice->id,
                                'part_code' => $partData['part_code'],
                                'part_name' => $partData['part_name'],
                                'quantity' => $partData['quantity'],
                                'price' => $partData['price'],
                                'eum' => $partData['eum'],
                                'total' => $total,
                            ]);
                        }
                    }
                }

                DB::commit();

                $message = 'Invoice manual berhasil diupdate';
                $logAction = 'Update Manual Invoice';
                $logDescription = 'Invoice manual ' . $request->no_invoice . ' berhasil diupdate';

                // Log the action
                LogHelper::createLog(
                    $logAction,
                    $logDescription,
                    'Invoice',
                    $request
                );

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'invoice' => $invoice
                ]);

            } catch (\Exception $exception) {
                DB::rollback();
                throw $exception;
            }

        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengupdate invoice: ' . $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Get manual invoices with their parts
     */
    public function getManualInvoices(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 10);
            $page = $request->get('page', 1);
            $search = $request->get('search', '');
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            $query = Invoice::with(['manualInvoiceParts.part', 'customerSales'])
                ->whereNotNull('direct_subtotal'); // Only manual invoices

            // Apply search filter
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('no_invoice', 'LIKE', "%{$search}%")
                      ->orWhere('customer', 'LIKE', "%{$search}%")
                      ->orWhere('po_number', 'LIKE', "%{$search}%");
                });
            }

            // Apply date filter
            if (!empty($dateFrom)) {
                $query->whereDate('tanggal_invoice', '>=', $dateFrom);
            }
            if (!empty($dateTo)) {
                $query->whereDate('tanggal_invoice', '<=', $dateTo);
            }

            $invoices = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            return response()->json([
                'success' => true,
                'invoices' => $invoices
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data invoice: ' . $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Get invoice details with parts
     */
    public function getInvoiceDetails($id)
    {
        try {
            $invoice = Invoice::with(['manualInvoiceParts.part', 'customerSales'])
                ->findOrFail($id);

            // Convert to array to avoid Carbon date casting issues
            $invoiceArray = $invoice->toArray();

            // Get raw date values from database without timezone conversion to prevent -1 day offset
            try {
                // Query the database directly to get raw date strings without Carbon casting
                $rawInvoice = DB::table('invoices')->where('id', $invoice->id)->first(['tanggal_invoice', 'due_date']);

                if ($rawInvoice && $rawInvoice->tanggal_invoice) {
                    // Extract just the date part (YYYY-MM-DD) from the raw database value
                    $invoiceArray['tanggal_invoice'] = substr($rawInvoice->tanggal_invoice, 0, 10);
                }

                if ($rawInvoice && $rawInvoice->due_date) {
                    // Extract just the date part (YYYY-MM-DD) from the raw database value
                    $invoiceArray['due_date'] = substr($rawInvoice->due_date, 0, 10);
                }

                Log::info('Manual Invoice raw date override successful - tanggal_invoice: ' . $invoiceArray['tanggal_invoice']);
            } catch (\Exception $dateException) {
                Log::error('Error getting raw dates for manual invoice: ' . $dateException->getMessage());
                // Fallback to Carbon formatting if raw values are not available
                if ($invoice->tanggal_invoice) {
                    $invoiceArray['tanggal_invoice'] = $invoice->tanggal_invoice->format('Y-m-d');
                }
                if ($invoice->due_date) {
                    $invoiceArray['due_date'] = $invoice->due_date->format('Y-m-d');
                }
            }

            return response()->json([
                'success' => true,
                'invoice' => $invoiceArray
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil detail invoice: ' . $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Print manual invoice PDF
     */
    public function printInvoice($id)
    {
        try {
            $invoice = Invoice::with(['manualInvoiceParts.part'])
                ->whereNotNull('direct_subtotal')
                ->findOrFail($id);

            // Prepare data for the invoice template
            $invoiceNumber = $invoice->no_invoice;
            $transactions = []; // Empty for manual invoices
            $primaryTransaction = null; // Not applicable for manual invoices

            // Calculate totals
            $subtotal = $invoice->direct_subtotal;
            $ppnRate = $invoice->ppn;
            $ppn = $subtotal * $ppnRate;
            $grandTotal = $subtotal + $ppn;

            // Format numbers
            $formattedSubtotal = number_format($subtotal, 0, ',', '.');
            $formattedPpn = number_format($ppn, 0, ',', '.');
            $formattedGrandTotal = number_format($grandTotal, 0, ',', '.');

            // Convert to words (terbilang)
            $terbilang = $this->convertToWords($grandTotal);

            // Prepare parts data for the template
            $allParts = [];
            if ($invoice->manualInvoiceParts && $invoice->manualInvoiceParts->count() > 0) {
                foreach ($invoice->manualInvoiceParts as $part) {
                    $allParts[] = [
                        'name' => $part->part_name,
                        'quantity' => $part->quantity,
                        'unit' => $part->eum,
                        'price' => $part->price,
                        'formattedPrice' => number_format($part->price, 0, ',', '.'),
                        'totalItem' => $part->total,
                        'formattedTotalItem' => number_format($part->total, 0, ',', '.'),
                        'unit_code' => '',
                        'part_code' => $part->part_code
                    ];
                }
            }

            // Generate PDF using the existing sales invoice template
            $pdf = Pdf::loadView('sales.invoice', compact(
                'transactions',
                'primaryTransaction',
                'terbilang',
                'invoiceNumber',
                'allParts',
                'formattedSubtotal',
                'formattedPpn',
                'formattedGrandTotal',
                'invoice',
                'subtotal',
                'ppnRate',
                'ppn'
            ));

            // Set paper size to portrait A4
            $pdf->setPaper('a4', 'portrait');

            // Sanitize invoice number for filename
            $safeInvoiceNumber = str_replace(['/', '\\'], '_', $invoiceNumber);

            // Stream the PDF (show in browser)
            return $pdf->stream('manual_invoice_' . $safeInvoiceNumber . '_' . Carbon::now()->format('YmdHis') . '.pdf');

        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mencetak invoice: ' . $exception->getMessage()
            ], 500);
        }
    }

    /**
     * Convert number to Indonesian words (terbilang)
     */
    private function convertToWords($number)
    {
        $number = (int) $number;

        if ($number == 0) return 'nol rupiah';

        $ones = ['', 'satu', 'dua', 'tiga', 'empat', 'lima', 'enam', 'tujuh', 'delapan', 'sembilan'];
        $teens = ['sepuluh', 'sebelas', 'dua belas', 'tiga belas', 'empat belas', 'lima belas', 'enam belas', 'tujuh belas', 'delapan belas', 'sembilan belas'];
        $tens = ['', '', 'dua puluh', 'tiga puluh', 'empat puluh', 'lima puluh', 'enam puluh', 'tujuh puluh', 'delapan puluh', 'sembilan puluh'];

        $result = '';

        // Billions
        if ($number >= 1000000000) {
            $billions = intval($number / 1000000000);
            $result .= $this->convertHundreds($billions, $ones, $teens, $tens) . ' milyar ';
            $number %= 1000000000;
        }

        // Millions
        if ($number >= 1000000) {
            $millions = intval($number / 1000000);
            $result .= $this->convertHundreds($millions, $ones, $teens, $tens) . ' juta ';
            $number %= 1000000;
        }

        // Thousands
        if ($number >= 1000) {
            $thousands = intval($number / 1000);
            if ($thousands == 1) {
                $result .= 'seribu ';
            } else {
                $result .= $this->convertHundreds($thousands, $ones, $teens, $tens) . ' ribu ';
            }
            $number %= 1000;
        }

        // Hundreds
        if ($number > 0) {
            $result .= $this->convertHundreds($number, $ones, $teens, $tens);
        }

        return trim($result) . ' rupiah';
    }

    private function convertHundreds($number, $ones, $teens, $tens)
    {
        $result = '';

        if ($number >= 100) {
            $hundreds = intval($number / 100);
            if ($hundreds == 1) {
                $result .= 'seratus ';
            } else {
                $result .= $ones[$hundreds] . ' ratus ';
            }
            $number %= 100;
        }

        if ($number >= 20) {
            $result .= $tens[intval($number / 10)] . ' ';
            $number %= 10;
        } elseif ($number >= 10) {
            $result .= $teens[$number - 10] . ' ';
            $number = 0;
        }

        if ($number > 0) {
            $result .= $ones[$number] . ' ';
        }

        return trim($result);
    }
}
